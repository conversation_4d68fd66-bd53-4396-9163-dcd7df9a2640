import type { Get, UniversalMiddleware } from "@universal-middleware/core";
import { dbInit } from "../database/drizzle/db";

declare global {
  namespace Universal {
    interface Context {
      db: DB;
    }
  }
}

// Add `db` to the Context
export const dbMiddleware: Get<[], UniversalMiddleware> = () => async (_request, context, _runtime) => {
  const db = dbInit();

  return {
    ...context,
    db: db,
  };
};
