import { getContext } from "@universal-middleware/hono";
import { Context } from "hono";
import { streamSSE } from "hono/streaming";
import { BlankEnv, BlankInput } from "hono/types";
import { QueueEvents } from "bullmq";
import { QGenerateVideo } from "@/jobs/queues";
import { getJob } from "@/database/drizzle/queries/jobs";
import { redisClient } from "@/lib/redis";

export async function notificationsHandler(c: Context<BlankEnv, "/notifications", BlankInput>) {
  return streamSSE(c, async (stream) => {
    try {
      const { session, db } = getContext(c);
      if (!session?.user) {
        return;
      }

      const generateVideoEvents = new QueueEvents(QGenerateVideo.name, { connection: redisClient });

      generateVideoEvents.on("active", async ({ jobId }) => {
        const [job] = await getJob(db, jobId);
        if (job.userId === session.user.id) {
          await stream.writeSSE({
            data: JSON.stringify({ status: "completed", job }),
            id: String(Date.now()),
          });
        }
      });

      generateVideoEvents.on("completed", async ({ jobId }) => {
        const [job] = await getJob(db, jobId);
        if (job.userId === session.user.id) {
          await stream.writeSSE({
            data: JSON.stringify({ status: "completed", job }),
            id: String(Date.now()),
          });
        }
      });

      generateVideoEvents.on("failed", (event) => {
        // Called whenever a job is moved to failed by any worker.
      });

      generateVideoEvents.on("progress", async (event) => {
        const [job] = await getJob(db, event.jobId);
        if (job.userId === session.user.id) {
          await stream.writeSSE({
            data: JSON.stringify({ status: "progress", job, data: event.data }),
            id: String(Date.now()),
          });
        }
      });

      while (true) {
        await stream.writeSSE({
          data: "Lub-dub",
          event: "heartbeat",
          id: String(Date.now()),
        });
        await stream.sleep(30000);
      }
    } catch (error) {
      console.log(error);
      await stream.close();
    }
  });
}
