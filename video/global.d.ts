import { dbInit } from "./database/drizzle/db";
import type { auth } from "./lib/auth";

declare module "telefunc" {
  namespace Telefunc {
    interface Context {
      db: DB;
      session?: Awaited<ReturnType<typeof auth.api.getSession>>;
    }
  }
}

declare global {
  type DB = ReturnType<typeof dbInit>;

  namespace Vike {
    interface PageContext {
      db: DB;
      session?: Awaited<ReturnType<typeof auth.api.getSession>>;
    }
  }
}

export {};
