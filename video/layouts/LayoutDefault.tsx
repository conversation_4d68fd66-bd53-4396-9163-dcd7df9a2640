import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { authClient } from "@/lib/auth-client";
import { cn } from "@/lib/utils";
import Cookies from "js-cookie";
import { useTranslation } from "react-i18next";
import { usePageContext } from "vike-react/usePageContext";
import z from "zod";
import { en, fr } from "zod/locales";
import placeholderUrl from "@/assets/placeholder.svg";
import { Link } from "@/components/Link.js";
import "./style.css";
import "./tailwind.css";
import { Video } from "lucide-react";
import { Notifications } from "@/components/Notifications";

export default function LayoutDefault({ children }: { children: React.ReactNode }) {
  return (
    <div className={"flex flex-col h-screen"}>
      <div className="flex flex-col flex-grow">
        <TopBar />
        <Content>{children}</Content>
      </div>
    </div>
  );
}

function TopBar() {
  const { t, i18n } = useTranslation();
  const { session } = usePageContext();

  return (
    <div className="bg-gray-50">
      <div className="max-w-5xl m-auto px-5 py-3 flex items-center gap-4">
        <Link href="/" className="text-3xl me-auto">
          Shorts
        </Link>
        <div className="flex items-center gap-4">
          {session?.user && (
            <>
              <Link href="/videos" className="flex items-center gap-2">
                <Video />
                {t("Videos")}
              </Link>
              <Notifications />
              <DropdownMenu>
                <DropdownMenuTrigger>
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={session.user.image || placeholderUrl} alt={session.user.name} />
                    <AvatarFallback>{session.user.name[0]}</AvatarFallback>
                  </Avatar>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuLabel>{session.user.email}</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <a href="/account/profile">{t("Account")}</a>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={async () => {
                      await authClient.signOut();
                      window.location.reload();
                    }}
                  >
                    {t("logout")}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          )}
          <Select
            value={i18n.resolvedLanguage || "fr"}
            onValueChange={(lang) => {
              i18n.changeLanguage(lang);
              Cookies.set("lang", lang);
              z.config(lang === "en" ? en() : fr());
            }}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="en">EN 🇬🇧</SelectItem>
              <SelectItem value="fr">FR 🇫🇷</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}

function Content({ children }: { children: React.ReactNode }) {
  const { urlPathname } = usePageContext();
  return (
    <div id="page-container" className={cn("overflow-auto", urlPathname === "/login" ? "my-auto " : "flex-1")}>
      <div id="page-content" className={"max-w-5xl m-auto p-5 pb-12"}>
        {children}
      </div>
    </div>
  );
}
