import "dotenv/config";
import { vike<PERSON>and<PERSON> } from "./server/vike-handler";
import { telefuncHandler } from "./server/telefunc-handler";
import { Hono } from "hono";
import { createHandler, createMiddleware } from "@universal-middleware/hono";
import { dbMiddleware } from "./server/db-middleware";
import { auth, authSessionMiddleware } from "./lib/auth";
import { notificationsHandler } from "./server/notifications-handler";

const app = new Hono();

app.use(createMiddleware(dbMiddleware)());

app.use(createMiddleware(authSessionMiddleware)());

app.on(["POST", "GET"], "/api/auth/**", (c) => auth.handler(c.req.raw));

app.post("/_telefunc", createHandler(telefuncHandler)());

app.get("/notifications", notificationsHandler);

/**
 * Vike route
 *
 * @link {@see https://vike.dev}
 **/
app.all("*", createHandler(vikeHandler)());

export default app;
