import { redisClient } from "@/lib/redis";
import { Processor, Queue, Worker } from "bullmq";
import { insertJob } from "@/database/drizzle/queries/jobs";

const options = {
  connection: redisClient,
};

export function w<TData>(queue: Queue<TData>, processor: Processor<TData>) {
  return () => {
    return new Worker<TData>(queue.name, processor, options);
  };
}

export async function addJob<TData>(db: DB, userId: string, queue: Queue<TData>, data: TData) {
  const job = (
    await insertJob(db, {
      name: queue.name,
      data: data,
      status: "pending",
      addedAt: new Date(),
      userId: userId,
    }).returning()
  )[0];
  return await queue.add(queue.name as any, data as any, { jobId: `${job.id}` });
}

export const QGenerateVideo = new Queue<{ videoId: number }>("GenerateVideo", options);
