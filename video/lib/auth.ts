import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { dbInit } from "@/database/drizzle/db"; // your drizzle instance
import type { Get, UniversalMiddleware } from "@universal-middleware/core";
import * as schema from "@/database/drizzle/schema/auth";

export const auth = betterAuth({
  database: drizzleAdapter(dbInit(), {
    provider: "pg",
    schema,
  }),
  emailAndPassword: {
    enabled: true,
  },
  socialProviders: {
    google: {
      prompt: "select_account",
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    },
  },
  trustedOrigins: ["http://localhost:5002", "https://shorts.bisso.app"],
});

declare global {
  namespace Universal {
    interface Context {
      session?: Awaited<ReturnType<typeof auth.api.getSession>>;
    }
  }
}

export const authSessionMiddleware: Get<[], UniversalMiddleware> = () => async (request, context) => {
  try {
    return {
      ...context,
      session: await auth.api.getSession({
        headers: request.headers,
      }),
    };
  } catch (error) {
    console.debug("authSessionMiddleware:", error);
    return {
      ...context,
      session: null,
    };
  }
};
