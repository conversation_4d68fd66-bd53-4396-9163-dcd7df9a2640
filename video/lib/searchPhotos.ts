import { createApi } from "unsplash-js";
import { createClient } from "pexels";
import createCallsiteRecord from "callsite-record";
import z from "zod";
import { fetch } from "undici";

if (!process.env.UNSPLASH_ACCESS_KEY) throw new Error("UNSPLASH_ACCESS_KEY is required");
if (!process.env.PEXELS_API_KEY) throw new Error("PEXELS_API_KEY is required");
if (!process.env.PIXBAY_API_KEY) throw new Error("PIXBAY_API_KEY is required");

const unsplash = createApi({
  accessKey: process.env.UNSPLASH_ACCESS_KEY,
});

const pexelsClient = createClient(process.env.PEXELS_API_KEY);

export async function searchPhotos(...args: Parameters<typeof unsplash.search.getPhotos>): Promise<{ url: string }[]> {
  const stackTrace = new Error("Stack Trace");
  Error.captureStackTrace(stackTrace, searchPhotos);
  try {
    const unsplashResult = await unsplash.search.getPhotos(...args).catch(() => null);
    if (unsplashResult?.response?.results.length)
      return unsplashResult.response.results.map((x) => ({ url: x.urls.full }));

    const pexelsResult = await pexelsClient.photos
      .search({ query: args[0].query, per_page: args[0].perPage || 1 })
      .catch(() => null);
    if (pexelsResult && "photos" in pexelsResult && pexelsResult.photos.length)
      return pexelsResult.photos.map((x) => ({ url: x.src.medium }));

    const pixabayResult = await fetch(
      `https://pixabay.com/api/?key=${process.env.PIXBAY_API_KEY}&q=${args[0].query}&per_page=${Math.min(Math.max(3, args[0].perPage ?? 0), 20)}`,
    );
    if (pixabayResult.ok) {
      const pixabayResultJson = z
        .object({ hits: z.array(z.object({ webformatURL: z.string() })) })
        .nullable()
        .catch(null)
        .parse(await pixabayResult.json().catch(() => null));
      if (pixabayResultJson?.hits?.length) return pixabayResultJson.hits.map((x: any) => ({ url: x.webformatURL }));
    }

    const openverseResult = await fetch(
      `https://api.openverse.org/v1/images/?format=json&q=${args[0].query}&page_size=${args[0].perPage || 1}`,
    );
    if (openverseResult.ok) {
      const openverseResultJson = z
        .object({ results: z.array(z.object({ url: z.string() })) })
        .nullable()
        .catch(null)
        .parse(await openverseResult.json().catch(() => null));
      if (openverseResultJson?.results?.length) return openverseResultJson.results.map((x: any) => ({ url: x.url }));
    }
  } catch (error) {
    console.log(
      error instanceof Error
        ? createCallsiteRecord({ forError: error })?.renderSync({
            stackFilter: (frame) => !frame.fileName?.includes("node_modules"),
          })
        : error,
    );
    console.log("Full stack stace:");
    console.log(createCallsiteRecord({ forError: stackTrace })?.renderSync({}));
  }

  return [];
  /*
  `https://api.openverse.org/v1/images/?format=json&q=`;
  */
}

export enum StockPhotoProvider {
  UNSPLASH = "unsplash",
  PEXELS = "pexels",
  PIXBAY = "pixabay",
  OPENVERSE = "openverse",
}

export async function searchStockPhotos(
  { provider, ...options }: Parameters<typeof unsplash.search.getPhotos>[0] & { provider?: StockPhotoProvider },
  additionalFetchOptions?: Parameters<typeof unsplash.search.getPhotos>[1],
): Promise<{ url: string }[]> {
  const stackTrace = new Error("Stack Trace");
  Error.captureStackTrace(stackTrace, searchPhotos);
  try {
    if (provider === StockPhotoProvider.UNSPLASH) {
      const unsplashResult = await unsplash.search.getPhotos(options, additionalFetchOptions).catch(() => null);
      if (unsplashResult?.response?.results.length) {
        const result = unsplashResult.response.results.map((x) => ({ url: x.urls.full }));
        return result;
      }
    } else if (provider === StockPhotoProvider.PEXELS) {
      const pexelsResult = await pexelsClient.photos
        .search({ query: options.query, per_page: options.perPage || 1 })
        .catch(() => null);
      if (pexelsResult && "photos" in pexelsResult && pexelsResult.photos.length) {
        const result = pexelsResult.photos.map((x) => ({ url: x.src.medium }));
        return result;
      }
    } else if (provider === StockPhotoProvider.PIXBAY) {
      const pixabayResult = await fetch(
        `https://pixabay.com/api/?key=${process.env.PIXBAY_API_KEY}&q=${options.query}&per_page=${Math.min(Math.max(3, options.perPage ?? 0), 20)}`,
      );
      if (pixabayResult.ok) {
        const pixabayResultJson = z
          .object({ hits: z.array(z.object({ webformatURL: z.string() })) })
          .nullable()
          .catch(null)
          .parse(await pixabayResult.json().catch(() => null));
        if (pixabayResultJson?.hits?.length) {
          const result = pixabayResultJson.hits.map((x: any) => ({ url: x.webformatURL }));
          return result;
        }
      }
    } else if (provider === StockPhotoProvider.OPENVERSE) {
      const openverseResult = await fetch(
        `https://api.openverse.org/v1/images/?format=json&q=${options.query}&page_size=${options.perPage || 1}`,
      );
      if (openverseResult.ok) {
        const openverseResultJson = z
          .object({ results: z.array(z.object({ url: z.string() })) })
          .nullable()
          .catch(null)
          .parse(await openverseResult.json().catch(() => null));
        if (openverseResultJson?.results?.length) {
          const result = openverseResultJson.results.map((x: any) => ({ url: x.url }));
          return result;
        }
      }
    }
    return [];
  } catch (error) {
    console.log(
      error instanceof Error
        ? createCallsiteRecord({ forError: error })?.renderSync({
            stackFilter: (frame) => !frame.fileName?.includes("node_modules"),
          })
        : error,
    );
    console.log("Full stack stace:");
    console.log(createCallsiteRecord({ forError: stackTrace })?.renderSync({}));
  }

  return [];
  /*
  `https://api.openverse.org/v1/images/?format=json&q=`;
  */
}
