import { ElevenLabsClient } from "@elevenlabs/elevenlabs-js";

export const elevenlabs = new ElevenLabsClient({
  apiKey: process.env.ELEVENLABS_API_KEY,
});

export function createSpeechWithTiming(voiceId: string, text: string) {
  return elevenlabs.textToSpeech.convertWithTimestamps(voiceId || "tnSpp4vdxKPjI9w0GnoV", {
    text,
    voiceSettings: {
      stability: 0.75,
    },
  });
}

let voicesCache: {
  voices: Awaited<ReturnType<typeof elevenlabs.voices.search>>;
  createdAt: Date;
} | null = null;

export async function getVoices(...[req, options]: Parameters<typeof elevenlabs.voices.search>) {
  if (!req && !options) {
    if (voicesCache && voicesCache.createdAt > new Date(Date.now() - 1000 * 60 * 60)) {
      return voicesCache.voices;
    }
    const res = await elevenlabs.voices.search();
    voicesCache = {
      voices: res,
      createdAt: new Date(),
    };
    return res;
  }
  return elevenlabs.voices.search(req, options);
}
