import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";

export interface BasePublicStorage {
  upload(buffer: Buffer, filepath: string, mimeType?: string): Promise<string>;
}

if (!process.env.AWS_S3_PUBLIC_BUCKET) throw new Error("AWS_S3_PUBLIC_BUCKET is required");
if (!process.env.AWS_REGION) throw new Error("AWS_REGION is required");

export const s3 = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

export const s3PublicMediaStorage: BasePublicStorage = {
  async upload(buffer: Buffer, filepath: string, mimeType?: string): Promise<string> {
    // upload to S3
    const command = new PutObjectCommand({
      Bucket: process.env.AWS_S3_PUBLIC_BUCKET,
      Key: filepath,
      Body: Buffer.from(buffer),
      ContentType: mimeType,
    });

    await s3.send(command);
    return `https://${process.env.AWS_S3_PUBLIC_BUCKET}.s3.${process.env.AWS_REGION}.amazonaws.com/${encodeURIComponent(filepath)}`;
  },
};
