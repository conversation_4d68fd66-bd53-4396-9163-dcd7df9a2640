{"compilerOptions": {"strict": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "module": "ESNext", "noEmit": true, "moduleResolution": "<PERSON><PERSON><PERSON>", "target": "ES2022", "lib": ["DOM", "DOM.Iterable", "ESNext"], "types": ["vite/client", "vike-react"], "jsx": "react-jsx", "jsxImportSource": "react", "paths": {"@/*": ["./*"]}}, "exclude": ["dist"]}