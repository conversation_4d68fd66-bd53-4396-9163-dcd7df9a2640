services:
  redis:
    image: redis:latest
    restart: always
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    networks:
      - shorts-network

  website:
    build:
      context: ./
      dockerfile: Dockerfile-prod
    container_name: shorts-website
    ports:
      - 5002:5002
    networks:
      - shorts-network
    env_file:
      - path: ./.env
        required: true
    restart: always

  jobs:
    build:
      context: ./
      dockerfile: Dockerfile-jobs-prod
    container_name: shorts-jobs
    networks:
      - shorts-network
    env_file:
      - path: ./.env
        required: true
    restart: always

networks:
  shorts-network:
    driver: bridge
