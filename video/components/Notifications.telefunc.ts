import { getPendingVideos } from "@/database/drizzle/queries/videos";
import { QGenerateVideo } from "@/jobs/queues";
import { errors } from "@/lib/errors";
import { getContext } from "telefunc";

export async function onGetPendingVideos() {
  const context = getContext();

  if (!context.session?.user) return { error: errors.NotAuthenticated };

  const pendingVideos = await getPendingVideos(context.db, context.session?.user.id);
  return await Promise.all(
    pendingVideos
      .filter((x) => x.jobId)
      .map(async (x) => {
        const job = await QGenerateVideo.getJob(x.jobId!);
        return { ...x, progress: job?.progress as { value: number; message: string } | null };
      }),
  );
}
