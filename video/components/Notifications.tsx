import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { BellDotIcon, BellIcon, FileVideoIcon, InboxIcon, Loader2Icon, VideotapeIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { onGetPendingVideos } from "./Notifications.telefunc";
import { tf, TFReturn } from "@/lib/tf";
import { useTranslation } from "react-i18next";

export function Notifications() {
  const { t } = useTranslation();
  const [pendingVideos, setPendingVideos] = useState<TFReturn<typeof onGetPendingVideos>>([]);
  const [isUnread, setIsUnread] = useState(false);

  useEffect(() => {
    tf(onGetPendingVideos).then((pendingVideos) => setPendingVideos(pendingVideos || []));
  }, []);

  useEffect(() => {
    const eventSource = new EventSource("/notifications");

    // Handle incoming data
    eventSource.onmessage = (event) => {
      setIsUnread(true);
      const data = JSON.parse(event.data);
      if (data?.job?.name === "GenerateVideo") {
        tf(onGetPendingVideos).then((pendingVideos) => setPendingVideos(pendingVideos || []));
      }
    };

    // Handle errors
    eventSource.onerror = () => {
      // setError("Connection lost. Trying to reconnect...");
      eventSource.close();
    };

    // Cleanup when component unmounts
    return () => eventSource.close();
  });

  return (
    <Popover>
      <PopoverTrigger onClick={() => setIsUnread(false)}>
        {isUnread ? <BellDotIcon className="animate-bounce" /> : <BellIcon />}
      </PopoverTrigger>
      <PopoverContent className="divide-y p-0">
        <h2 className="text-lg font-bold p-4">{t("Notifications")}</h2>
        <div className="divide-y">
          {pendingVideos.map((x) => (
            <div key={x.id} className="flex items-center gap-2 p-4">
              <FileVideoIcon className="h-8 w-8" />
              <div>
                <div className="text-sm">{t("Generating video")}</div>
                {x.progress?.message && (
                  <div className="text-xs flex items-center gap-2">
                    <Loader2Icon className="animate-spin h-4 w-4" />
                    <span>{x.progress?.message}</span>
                  </div>
                )}
                <div className="font-bold text-xs">{x.subject}</div>
              </div>
            </div>
          ))}
          {pendingVideos.length === 0 && (
            <div className="p-8 flex flex-col gap-2 items-center justify-center">
              <InboxIcon className="text-gray-500 h-16 w-16" />
              {t("No notifications")}
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}
