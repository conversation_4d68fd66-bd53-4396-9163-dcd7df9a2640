import { ComponentProps } from "react";
import { usePageContext } from "vike-react/usePageContext";

export function Link({ href, children, ...props }: Omit<ComponentProps<"a">, "href"> & { href: string }) {
  const pageContext = usePageContext();
  const { urlPathname } = pageContext;
  const isActive = href === "/" ? urlPathname === href : urlPathname.startsWith(href);
  return (
    <a href={href} className={isActive ? "is-active" : undefined} {...props}>
      {children}
    </a>
  );
}
