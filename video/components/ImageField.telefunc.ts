import { getAllFiles } from "@/database/drizzle/queries/files";
import { errors } from "@/lib/errors";
import { searchStockPhotos } from "@/lib/searchPhotos";
import { getContext } from "telefunc";

export async function onSearchStockPhotos(...args: Parameters<typeof searchStockPhotos>) {
  const context = getContext();

  if (!context.session?.user) return { error: errors.NotAuthenticated };

  return await searchStockPhotos(...args);
}

export async function onGetMyUploads() {
  const context = getContext();

  if (!context.session?.user) return { error: errors.NotAuthenticated };

  return await getAllFiles(context.db, {
    type: "image",
    userId: context.session.user.id,
  });
}
