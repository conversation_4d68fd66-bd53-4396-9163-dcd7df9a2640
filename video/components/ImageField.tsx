import { Sheet, She<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON>Trigger } from "@/components/ui/sheet";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>L<PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useEffect, useState } from "react";
import { ControllerProps, FieldPath, FieldValues } from "react-hook-form";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "./ui/form";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";
import Dropzone from "react-dropzone";
import { useTranslation } from "react-i18next";
import { useDebounce } from "@uidotdev/usehooks";
import { onGetMyUploads, onSearchStockPhotos } from "./ImageField.telefunc";
import { tf } from "@/lib/tf";
import { Button } from "./ui/button";
import type { StockPhotoProvider } from "@/lib/searchPhotos";
import { ImagesIcon } from "lucide-react";

export function ImageField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
  I extends typeof Input | typeof Textarea = typeof Input,
>({
  label,
  children,
  ...props
}: Omit<ControllerProps<TFieldValues, TName>, "render"> & {
  label: string;
  children?: (setShow: (show: boolean) => void) => React.ReactNode;
}) {
  const { t } = useTranslation();
  const [show, setShow] = useState(false);

  return (
    <FormField
      {...props}
      render={({ field }) => {
        const onChange = (url: string) => {
          field.onChange(url);
          setShow(false);
        };
        return (
          <FormItem>
            <FormControl>
              {/* <InputTag placeholder={label} {...field} {...(input?.props as any)} /> */}
              <Sheet open={show} onOpenChange={setShow}>
                {children?.(setShow) || (
                  <SheetTrigger className="cursor-pointer flex flex-col gap-2 justify-center" onBlur={field.onBlur}>
                    <FormLabel>{label}</FormLabel>
                    <img src={field.value} className="w-32 aspect-[9/16] object-cover rounded-xl" />
                  </SheetTrigger>
                )}
                <SheetContent side="left" className="min-w-[min(100%,45rem)] flex flex-col">
                  <SheetHeader>
                    <SheetTitle>{t("Select Image")}</SheetTitle>
                    <SheetDescription>
                      {t("Upload or select images from your library or stock photos.")}
                    </SheetDescription>
                  </SheetHeader>
                  <Tabs defaultValue="account" className="mt-4 min-h-0 basis-0 grow shrink">
                    <TabsList>
                      <TabsTrigger value="stock-photos">{t("Stock photos")}</TabsTrigger>
                      <TabsTrigger value="library">{t("Library")}</TabsTrigger>
                      <TabsTrigger value="upload">{t("Upload")}</TabsTrigger>
                    </TabsList>
                    <TabsContent value="stock-photos" className="space-y-4 min-h-0 basis-0 grow shrink flex flex-col">
                      <StockPhotos onChange={onChange} />
                    </TabsContent>
                    <TabsContent value="library" className="space-y-4 min-h-0 basis-0 grow shrink flex flex-col">
                      <Library onChange={onChange} />
                    </TabsContent>
                    <TabsContent value="upload">
                      <Upload onChange={onChange} />
                    </TabsContent>
                  </Tabs>
                </SheetContent>
              </Sheet>
            </FormControl>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}

const providers = [
  "unsplash",
  "pexels",
  "pixabay",
  "openverse",
] as const;

export function StockPhotos({ onChange }: { onChange: (url: string) => void }) {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [provider, setProvider] = useState<(typeof providers)[number]>(providers[0]);
  const [isSearching, setIsSearching] = useState(false);
  const [results, setResults] = useState<{ url: string }[]>([]);
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  useEffect(() => {
    const search = async () => {
      if (!debouncedSearchTerm) return;
      setIsSearching(true);
      const data = await tf(onSearchStockPhotos, {
        query: debouncedSearchTerm,
        provider: provider as StockPhotoProvider,
      });
      const results = data || [];
      setIsSearching(false);
      setResults(results);
    };
    search();
  }, [debouncedSearchTerm, provider]);

  return (
    <>
      <Input placeholder={t("Search")} value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
      <div className="flex gap-1 flex-wrap">
        {providers.map((x) => (
          <Button
            key={x}
            type="button"
            variant={x === provider ? "default" : "outline"}
            disabled={x === provider}
            onClick={() => setProvider(x)}
          >
            {x}
          </Button>
        ))}
      </div>
      <div className="grid grid-cols-[repeat(auto-fill,minmax(300px,1fr))] gap-3 min-h-0 basis-0 grow shrink overflow-y-auto p-4">
        {results.map((x) => (
          <button
            key={x.url}
            type="button"
            onClick={() => onChange(x.url)}
            className="p-3 rounded-xl outline space-y-2 relative bg-white hover:bg-gray-50"
          >
            <img src={x.url} className="w-full aspect-[9/16] object-cover rounded-xl" />
          </button>
        ))}
      </div>
    </>
  );
}

export function Library({ onChange }: { onChange: (url: string) => void }) {
  const { t } = useTranslation();
  const [results, setResults] = useState<{ url: string }[]>([]);

  useEffect(() => {
    tf(onGetMyUploads).then((res) => setResults(res || []));
  }, []);

  return results.length ? (
    <div className="grid grid-cols-[repeat(auto-fill,minmax(300px,1fr))] gap-3 min-h-0 basis-0 grow shrink overflow-y-auto p-4">
      {results.map((x) => (
        <button
          key={x.url}
          type="button"
          onClick={() => onChange(x.url)}
          className="p-3 rounded-xl outline space-y-2 relative bg-white hover:bg-gray-50"
        >
          <img src={x.url} className="w-full aspect-[9/16] object-cover rounded-xl" />
        </button>
      ))}
    </div>
  ) : (
    <div className="p-8 flex flex-col gap-2 items-center justify-center">
      <ImagesIcon className="text-gray-500 h-16 w-16" />
      {t("No images uploaded yet")}
    </div>
  );
}

const toBase64 = (file: File) =>
  new Promise<string>((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
  });

export function Upload({ onChange }: { onChange: (url: string) => void }) {
  const { t } = useTranslation();
  return (
    <div>
      <Dropzone
        onDrop={async (acceptedFiles) => {
          if (acceptedFiles.length === 0) return;
          onChange(await toBase64(acceptedFiles[0]));
        }}
      >
        {({ getRootProps, getInputProps }) => (
          <section>
            <div {...getRootProps()} className="px-4 py-16 border rounded-md border-dashed text-center">
              <input {...getInputProps()} />
              <p>{t("Drag & drop some files here, or click to select files")}</p>
            </div>
          </section>
        )}
      </Dropzone>
    </div>
  );
}
