// We use Telefunc (https://telefunc.com) for data mutations. Being able to use Telefunc for fetching initial data is work-in-progress (https://vike.dev/data-fetching#tools).

import { VideoIdeaInsert, VideoInsert } from "@/database/drizzle/schema/videos";
import { addJob, QGenerateVideo } from "@/jobs/queues";
import { getVoices } from "@/lib/elevenlabs";
import { getContext } from "telefunc";
import * as drizzleQueries from "../../database/drizzle/queries/videos";
import { errors } from "@/lib/errors";
import { ai } from "@/lib/ai";
import z from "zod";

export async function onNewVideo(data: VideoInsert) {
  const context = getContext();

  if (!context.session?.user.id) return { error: errors.NotAuthenticated };

  data.voiceId ||= "tnSpp4vdxKPjI9w0GnoV";
  data.userId = context.session.user.id;
  let record = (await drizzleQueries.insertVideo(context.db, data).returning())[0];

  await addJob(context.db, context.session.user.id, QGenerateVideo, { videoId: record.id });

  return record;
}

export async function onGetTargetSuggestion(theme: string) {
  const { output } = await ai.getStructuredOutput({
    model: "sonar",
    prompt: `Suggest 3 completions for the following prompt:
       Create a script for ${theme} in short video format for <rest of prompt>.
       Only give the <rest of prompt> part in suggestions, try to be less specific.
       `,
    maxTokens: 10000,
    schema: z.object({ suggestions: z.array(z.string()) }),
  });

  return output;
}

export async function onGetVideoTitles(data: VideoIdeaInsert) {
  const { output } = await ai.getStructuredOutput({
    model: "sonar",
    prompt: `Give exactly ${data.days * data.frequency} titles for videos 
       of type ${data.theme} in short video format for ${data.target}.
       `,
    maxTokens: 10000,
    schema: z.object({ titles: z.array(z.string()) }),
  });

  return output;
}

export async function onSaveVideoIdea(data: VideoIdeaInsert) {
  const context = getContext();
  return await drizzleQueries.insertVideoIdea(context.db, data);
}

export async function onGetMoreVoices(...args: Parameters<typeof getVoices>) {
  return await getVoices(...args);
}
