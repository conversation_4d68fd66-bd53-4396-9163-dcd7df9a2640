import { errors } from "@/lib/errors";
import { connectToAccount, disconnectFromAccount, syncAccounts } from "@/lib/getlate";
import { getContext } from "telefunc";

export async function onGetConnectionLink(socialPlatform: string) {
  const context = getContext();

  if (!context.session?.user) return { error: errors.NotAuthenticated };

  return await connectToAccount(context.db, context.session?.user, socialPlatform);
}

export async function onSyncGetlateAccounts() {
  const context = getContext();

  if (!context.session?.user) return { error: errors.NotAuthenticated };

  return await syncAccounts(context.db, context.session?.user.id);
}

export async function onDisconnectAccount(accountId: string, profileId: string) {
  const context = getContext();

  if (!context.session?.user) return { error: errors.NotAuthenticated };

  return await disconnectFromAccount(context.db, accountId, profileId);
}
