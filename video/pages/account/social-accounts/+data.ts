import { getOrCreateProfile } from "@/lib/getlate";
import type { PageContextServer } from "vike/types";

export type Data = Awaited<ReturnType<typeof data>>;

export default async function data(_pageContext: PageContextServer) {
  if (!_pageContext.session?.user) throw new Error("Not authenticated");
  const getlateProfile = await getOrCreateProfile(_pageContext.db, _pageContext.session?.user);

  return { getlateProfile };
}
