import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { onDisconnectAccount, onGetConnectionLink, onSyncGetlateAccounts } from "./+Page.telefunc";
import { usePageContext } from "vike-react/usePageContext";
import { useEffect, useMemo, useState } from "react";
import { useData } from "vike-react/useData";
import { Data } from "./+data";
import { Button } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import { platforms } from "./platforms";
import { navigate } from "vike/client/router";
import { tf } from "@/lib/tf";

export default function Page() {
  const { getlateProfile } = useData<Data>();
  const { urlParsed } = usePageContext();
  const { t } = useTranslation();
  const [accounts, setAccounts] = useState(getlateProfile?.accounts);
  const accountsMap = useMemo(() => {
    return new Map((accounts || []).map((x) => [x.platform.toLowerCase(), x]));
  }, [accounts]);

  useEffect(() => {
    if (urlParsed.search.connected) {
      tf(onSyncGetlateAccounts).then((getlateProfile) => {
        if (getlateProfile?.accounts) {
          setAccounts(getlateProfile.accounts);
          navigate("/account/social-accounts");
        }
      });
    }
  }, [urlParsed.search.connected]);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{t("Social accounts")}</CardTitle>
            <CardDescription>{t("Connect your social media profiles to auto post your videos")}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-[repeat(auto-fit,minmax(12rem,1fr))] gap-4">
          {platforms.map((platform) => (
            <PlatformCard
              key={platform.name}
              platform={platform}
              accountsMap={accountsMap}
              getlateProfile={getlateProfile}
              setAccounts={setAccounts}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

type Account = Data["getlateProfile"]["accounts"][number];

function PlatformCard({
  platform,
  accountsMap,
  getlateProfile,
  setAccounts,
}: {
  platform: (typeof platforms)[0];
  accountsMap: Map<string, Account>;
  getlateProfile: Data["getlateProfile"];
  setAccounts: React.Dispatch<React.SetStateAction<Account[]>>;
}) {
  const { t } = useTranslation();
  const { urlParsed } = usePageContext();
  const [isLoading, setIsLoading] = useState(false);
  const platformName = platform.name.toLowerCase();
  const account = accountsMap.get(platform.name.toLowerCase());
  const isInProcess = isLoading || urlParsed.search.connected === platformName;

  return (
    <Card key={platform.name}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {<platform.icon style={{ color: platform.color }} />}
          {platform.name}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Button
          key={platform.name}
          onClick={async () => {
            setIsLoading(true);
            try {
              if (account) {
                await tf(onDisconnectAccount, account.getlateAccountId, getlateProfile?.getlateProfileId!);
                setAccounts((arr) => arr.filter((x) => x.getlateAccountId !== account.getlateAccountId));
              } else {
                const res = await tf(onGetConnectionLink, platform.name);
                if (res?.authUrl) {
                  window.location.href = res?.authUrl;
                }
              }
            } catch (error) {
              console.error(error);
            }
            setIsLoading(false);
          }}
          disabled={isInProcess}
          loading={isInProcess}
          variant={account ? undefined : "outline"}
        >
          {account ? t("Disconnect") : t("Connect")}
        </Button>
      </CardContent>
    </Card>
  );
}
