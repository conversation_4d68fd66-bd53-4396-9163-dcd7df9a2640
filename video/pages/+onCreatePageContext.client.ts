import type { PageContextClient } from "vike/types";
import Cookies from "js-cookie";
import type { i18n } from "i18next";
import { initI18n } from "@/lib/i18n";

// This hook is called upon new incoming HTTP requests
export async function onCreatePageContext(pageContext: PageContextClient) {
  pageContext.i18n = await initI18n(pageContext.lang ?? Cookies.get("lang") ?? "en");
}

declare global {
  namespace Vike {
    interface PageContext {
      i18n: i18n;
    }
  }
}
