import { I18nextProvider } from "react-i18next";
import { usePageContext } from "vike-react/usePageContext";
import { Toaster } from "@/components/ui/sonner";

export default function StoreProvider({ children }: { children: React.ReactNode }) {
  const { i18n } = usePageContext();

  return (
    <I18nextProvider i18n={i18n}>
      {children}
      <Toaster position="top-center" />
    </I18nextProvider>
  );
}
