import { usePageContext } from "vike-react/usePageContext";
import { useTranslation } from "react-i18next";

export default function Page() {
  const { t } = useTranslation();
  const { is404 } = usePageContext();
  if (is404) {
    return (
      <>
        <h1>{t("404 Page Not Found")}</h1>
        <p>{t("This page could not be found.")}</p>
      </>
    );
  }
  return (
    <>
      <h1>{t("500 Internal Server Error")}</h1>
      <p>{t("Something went wrong.")}</p>
    </>
  );
}
