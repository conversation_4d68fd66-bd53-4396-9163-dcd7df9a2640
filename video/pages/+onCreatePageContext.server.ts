import type { PageContextServer } from "vike/types";
import { parse as parseCookie } from "cookie-es";
import { initI18n } from "@/lib/i18n";

// This hook is called upon new incoming HTTP requests
export async function onCreatePageContext(pageContext: PageContextServer) {
  const cookies = pageContext.headers?.cookie ? parseCookie(pageContext.headers?.cookie) : {};

  pageContext.lang = cookies.lang || "en";
  pageContext.i18n = await initI18n(pageContext.lang);
}

declare global {
  namespace Vike {
    interface PageContext {
      lang: string;
    }
  }
}
