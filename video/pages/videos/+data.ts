// https://vike.dev/data
import { VideoItem } from "@/database/drizzle/schema/videos";
import * as drizzleQueries from "../../database/drizzle/queries/videos";
import type { PageContextServer } from "vike/types";
import { getOrCreateProfile } from "@/lib/getlate";

export type Data = Awaited<ReturnType<typeof data>>;

export default async function data(_pageContext: PageContextServer) {
  if (!_pageContext.session?.user) throw new Error("Not authenticated");
  const video = await drizzleQueries.getAllVideos(_pageContext.db, _pageContext.session.user.id);
  const getlateProfile = await getOrCreateProfile(_pageContext.db, _pageContext.session.user);

  return { video, getlateProfile };
}
