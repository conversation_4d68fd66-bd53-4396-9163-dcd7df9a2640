import type { Data } from "./+data";
import { useData } from "vike-react/useData";
import { VideoList } from "./VideoList.js";
import { useTranslation } from "react-i18next";

export default function Page() {
  const data = useData<Data>();
  const { t } = useTranslation();

  return (
    <>
      <h1 className="mb-4 text-2xl font-bold">{t("Videos")}</h1>
      <VideoList initialVideoItems={data.video} />
    </>
  );
}
