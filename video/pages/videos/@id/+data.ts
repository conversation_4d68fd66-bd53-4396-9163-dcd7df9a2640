// https://vike.dev/data

import type { PageContextServer } from "vike/types";
import { useConfig } from "vike-react/useConfig";
import { getVideo } from "@/database/drizzle/queries/videos";
import { getVoices } from "@/lib/elevenlabs";

export type Data = Awaited<ReturnType<typeof data>>;

export const data = async (pageContext: PageContextServer) => {
  // https://vike.dev/useConfig
  const config = useConfig();

  const [video] = await getVideo(pageContext.db, +pageContext.routeParams.id);
  const voices = await getVoices();

  return { video, voices };
};
