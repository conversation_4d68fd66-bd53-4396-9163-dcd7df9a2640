// We use Telefunc (https://telefunc.com) for data mutations. Being able to use Telefunc for fetching initial data is work-in-progress (https://vike.dev/data-fetching#tools).

import * as drizzleQueries from "@/database/drizzle/queries/videos";
import { VideoItem } from "@/database/drizzle/schema/videos";
import { addJob, QGenerateVideo } from "@/jobs/queues";
import { getVoices } from "@/lib/elevenlabs";
import { errors } from "@/lib/errors";
import { getContext } from "telefunc";
import { z } from "zod";

const sectionSchema = z.object({
  imageDescription: z.string(),
  voiceover: z.string(),
  imageUrl: z.string().optional(),
});

const transcriptSchema = z.object({ sections: z.array(sectionSchema) });

export async function onUpdateAndGenerateVideo(id: number, data: Partial<VideoItem>) {
  const context = getContext();

  if (!context.session?.user.id) return { error: errors.NotAuthenticated };

  data.voiceId ||= "tnSpp4vdxKPjI9w0GnoV";
  let record = (await drizzleQueries.updateVideo(context.db, id, data).returning())[0];

  await addJob(context.db, context.session.user.id, QGenerateVideo, { videoId: record.id });

  return record;
}

export async function onGetMoreVoices(...args: Parameters<typeof getVoices>) {
  return await getVoices(...args);
}
