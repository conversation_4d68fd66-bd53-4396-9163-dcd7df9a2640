import { getGetlateProfile } from "@/database/drizzle/queries/getlate";
import { getVideo } from "@/database/drizzle/queries/videos";
import { errors } from "@/lib/errors";
import { sharePost } from "@/lib/getlate";
import { getContext } from "telefunc";

export async function onShare(id: number, platforms?: string[], scheduledAt?: string) {
  const context = getContext();

  if (!context.session?.user) return { error: errors.NotAuthenticated };
  const video = (await getVideo(context.db, id))[0];
  if (!video.videoUrl) return { error: errors.InvalidVideo };
  const getlateProfile = await getGetlateProfile(context.db, context.session?.user.id);

  if (!getlateProfile?.accounts.length) return { error: errors.NoConnectedSocialAccount };
  let accounts = getlateProfile.accounts;
  if (platforms?.length) {
    accounts = accounts.filter((x) => platforms.includes(x.platform.toLowerCase()));
  }

  const res = await sharePost({
    content: "Check out this video I generated with AI",
    platforms: accounts.map((x) => ({
      platform: x.platform.toLowerCase(),
      accountId: x.getlateAccountId,
    })),
    scheduledFor: scheduledAt || new Date().toISOString(),
    timezone: "UTC",
    publishNow: true,
    isDraft: false,
    visibility: "public",
    tags: [],
    mediaItems: [
      {
        type: "video",
        url: video.videoUrl,
        filename: "video.mp4",
      },
    ],
  });

  return res.data;
}
