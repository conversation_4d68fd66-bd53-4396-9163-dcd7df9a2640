{"scripts": {"dev": "vike dev --port 5002", "build": "vike build", "preview": "cross-env NODE_ENV=production tsx ./hono-entry.node.ts", "dev:jobs": "tsx watch jobs/index.ts", "build:jobs": "esbuild jobs/index.ts --bundle --outfile=dist/jobs/index.cjs --platform=node --format=cjs", "preview:jobs": "cross-env NODE_ENV=production node dist/jobs/index.cjs", "drizzle:generate": "drizzle-kit generate", "drizzle:migrate": "drizzle-kit migrate", "drizzle:studio": "drizzle-kit studio", "lint": "eslint .", "translate": "tsx translate.ts", "shadcn": "npx shadcn@latest"}, "dependencies": {"@aws-sdk/client-s3": "^3.876.0", "@elevenlabs/elevenlabs-js": "^2.12.2", "@hono/node-server": "^1.19.0", "@hono/zod-validator": "^0.7.2", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@tiptap/pm": "^3.3.0", "@tiptap/react": "^3.3.0", "@tiptap/starter-kit": "^3.3.0", "@uidotdev/usehooks": "^2.4.1", "@universal-middleware/core": "^0.4.9", "@universal-middleware/hono": "^0.4.16", "@vitejs/plugin-react": "^5.0.1", "axios": "^1.11.0", "better-auth": "^1.3.7", "bullmq": "^5.58.2", "callsite-record": "^4.1.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookie-es": "^2.0.0", "cross-env": "^10.0.0", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.5", "es-toolkit": "^1.39.10", "fast-json-stable-stringify": "^2.1.0", "file-type": "^21.0.0", "hono": "^4.9.4", "html-to-text": "^9.0.5", "i18next": "^25.4.2", "ioredis": "^5.7.0", "jose": "^6.0.13", "js-cookie": "^3.0.5", "lucide-react": "^0.542.0", "marked": "^16.2.0", "next-themes": "^0.4.6", "pexels": "^1.4.0", "pg": "^8.16.3", "react": "^19.1.1", "react-dom": "^19.1.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.62.0", "react-i18next": "^15.7.2", "react-icons": "^5.5.0", "rm": "^0.1.8", "sonner": "^2.0.7", "stripe": "^18.5.0", "sweetalert2": "^11.22.5", "tailwind-merge": "^3.3.1", "telefunc": "^0.2.11", "tw-animate-css": "^1.3.7", "undici": "^7.15.0", "unsplash-js": "^7.0.19", "vaul": "^1.1.2", "vike": "^0.4.237", "vike-react": "^0.6.5", "zod": "^4.1.3"}, "devDependencies": {"@eslint/js": "^9.34.0", "@hono/vite-dev-server": "^0.20.1", "@ianvs/prettier-plugin-sort-imports": "^4.6.2", "@tailwindcss/vite": "^4.1.12", "@types/html-to-text": "^9.0.4", "@types/js-cookie": "^3.0.6", "@types/node": "^20.19.11", "@types/pg": "^8.15.5", "@types/react": "^19.1.11", "@types/react-dom": "^19.1.8", "@vitalets/google-translate-api": "^9.2.1", "drizzle-kit": "^0.31.4", "esbuild": "^0.25.9", "eslint": "^9.34.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "globals": "^16.3.0", "google-translate-api": "^2.3.0", "inquirer": "^12.9.4", "prettier": "^3.6.2", "prettier-plugin-multiline-arrays": "^4.0.3", "tailwindcss": "^4.1.12", "ts-morph": "^26.0.0", "tsx": "^4.20.5", "typescript": "^5.9.2", "typescript-eslint": "^8.41.0", "vite": "^7.1.3"}, "type": "module"}