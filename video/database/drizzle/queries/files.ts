import { FileItem, fileTable } from "../schema/files";
import crypto from "crypto";
import { fileType<PERSON>rom<PERSON>uffer } from "file-type";
import path from "path";
import { s3PublicMediaStorage } from "@/lib/storage";
import { eq, and } from "drizzle-orm";

export async function uploadFile(db: DB, userId: string, buffer: Buffer, filename: string) {
  const randomBytes = crypto.randomBytes(16);
  const randomName = randomBytes.toString("hex");

  const fileType = await fileTypeFromBuffer(buffer);
  if (!path.extname(filename) && fileType?.ext) filename += `.${fileType.ext}`;

  return (
    await db
      .insert(fileTable)
      .values({
        url: await s3PublicMediaStorage.upload(buffer, `${randomName}-${filename}`, fileType?.mime),
        userId,
        mime: fileType?.mime,
        type: fileType?.mime?.startsWith("image") ? "image" : "video",
      })
      .returning()
  )[0];
}

export function getAllFiles(db: DB, query?: Partial<FileItem>) {
  let conditions = [];

  if (query) {
    for (const [key, value] of Object.entries(query)) {
      if (value !== undefined) {
        // @ts-expect-error - TS doesn’t know key matches a column
        conditions.push(eq(fileTable[key], value));
      }
    }
  }
  return db
    .select()
    .from(fileTable)
    .where(conditions.length ? and(...conditions) : undefined);
}
