import { relations } from "drizzle-orm";
import { integer, pgTable, text, timestamp } from "drizzle-orm/pg-core";
import { user } from "./auth";

export const userRelations = relations(user, ({ one }) => ({
  getLateProfile: one(getlateProfileTable),
}));

export const getlateProfileTable = pgTable("getlate_profiles", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  userId: text().references(() => user.id),
  getlateProfileId: text().notNull(),
  createdAt: timestamp().defaultNow(),
});

export type GetlateProfileItem = typeof getlateProfileTable.$inferSelect;
export type GetlateProfileInsert = typeof getlateProfileTable.$inferInsert;

export const getlateProfileRelations = relations(getlateProfileTable, ({ one, many }) => ({
  user: one(user, {
    fields: [getlateProfileTable.userId],
    references: [user.id],
  }),
  accounts: many(getlateAccountTable),
}));

export const getlateAccountTable = pgTable("getlate_accounts", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  profileId: integer()
    .notNull()
    .references(() => getlateProfileTable.id),
  getlateAccountId: text().notNull(),
  platform: text().notNull(),
  createdAt: timestamp().defaultNow(),
});

export type GetlateAccountItem = typeof getlateAccountTable.$inferSelect;
export type GetlateAccountInsert = typeof getlateAccountTable.$inferInsert;

export const getlateAccountRelations = relations(getlateAccountTable, ({ one }) => ({
  profile: one(getlateProfileTable, {
    fields: [getlateAccountTable.profileId],
    references: [getlateProfileTable.id],
  }),
}));
