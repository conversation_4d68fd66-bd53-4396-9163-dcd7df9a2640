import { integer, pgTable, text, timestamp } from "drizzle-orm/pg-core";
import { user } from "./auth";
import { relations } from "drizzle-orm";

export const fileTable = pgTable("files", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  url: text().notNull(),
  mime: text(),
  type: text({ enum: ["image", "video"] }),
  userId: text().references(() => user.id),
  createdAt: timestamp().defaultNow(),
});

export type FileItem = typeof fileTable.$inferSelect;
export type FileInsert = typeof fileTable.$inferInsert;

export const userRelations = relations(user, ({ many }) => ({
  files: many(fileTable),
}));

export const fileRelations = relations(fileTable, ({ one }) => ({
  user: one(user, {
    fields: [fileTable.userId],
    references: [user.id],
  }),
}));
